{"private": true, "type": "module", "scripts": {"dev": "./scripts/prepare.sh && npm run type-check && npm run lint && vite", "build": "./scripts/prepare.sh && vite build", "serve": "vite preview", "type-check": "tsc --build --noEmit", "lint": "biome lint", "format": "biome format", "check": "biome check", "check:fix": "biome check --fix"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^7.3.0", "@mui/material": "^7.3.0", "ra-data-simple-rest": "^5.10.1", "react": "^19.1.1", "react-admin": "^5.10.1", "react-dom": "^19.1.1", "react-router": "^7.7.1", "react-router-dom": "^7.7.1"}, "devDependencies": {"@biomejs/biome": "^2.1.3", "@types/node": "^24.2.0", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^4.7.0", "typescript": "^5.9.2", "vite": "^7.0.6"}, "name": "internal-dashboard"}