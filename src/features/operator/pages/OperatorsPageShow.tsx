import { Box, Divider, Typography } from '@mui/material'
import { BooleanField, Show, SimpleShowLayout, TextField } from 'react-admin'
import { en } from '../../../i18n/en'
import { ResendEnrollEmailButton } from '../components/ResendEnrollEmailButton'
import { resources } from '../resources'

export const OperatorsPageShow = () => {
  return (
    <Show
      queryOptions={{
        // disables CRUD fetching because our backend does not support that
        // for this API
        enabled: false,
      }}
    >
      <Box padding={2}>
        <Typography>{en.common.group.userInfo}</Typography>
        <Divider />
        <SimpleShowLayout direction='row' spacing={10} alignItems={'center'}>
          <TextField source='user_id' />
          <TextField source='name' />
          <Divider />
          <TextField source='email' />
          <TextField source='current_status' />
          <BooleanField source='is_logged_in' />
          <ResendEnrollEmailButton resource={resources.operatorsResendEmail} />
        </SimpleShowLayout>
      </Box>
    </Show>
  )
}
