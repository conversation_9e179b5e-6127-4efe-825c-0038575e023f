import { Stack } from '@mui/material'
import { Show, SimpleShowLayout } from 'react-admin'
import { ApproveActionDialog } from '../../../common/components/ApproveActionDialog'
import { DenyActionDialog } from '../../../common/components/DenyActionDialog'
import { SectionHeader } from '../../../common/components/SectionHeader'
import { en } from '../../../i18n/en'
import { AddressDocument } from '../components/AddressDocument'
import { UserAddressDetails } from '../components/UserAddressDetails'
import { UserDetails } from '../components/UserDetails'
import { resources } from '../resources'
import { addressDenyOptions } from '../utils/consts'

export const AddressInfoShow = () => {
  return (
    <Show>
      <SimpleShowLayout>
        <SectionHeader>{en.common.group.userInfo}</SectionHeader>
        <UserDetails />
        <SectionHeader>{en.kyc.group.addressSubmission}</SectionHeader>
        <UserAddressDetails />
        <SectionHeader>{en.kyc.group.addressDocument}</SectionHeader>
        <AddressDocument />
        <SectionHeader>{en.kyc.group.reviewDocument}</SectionHeader>
        <Stack direction='row' spacing={10}>
          <ApproveActionDialog
            recordKey='user_details.user_id'
            resource={resources.addressApprove}
            redirectTo={`/${resources.addressReviewListAll}`}
            approvedActionMsg={en.common.feedback.documentApproved}
          />
          <DenyActionDialog
            recordKey='user_details.user_id'
            resource={resources.addressDeny}
            redirectTo={`/${resources.addressReviewListAll}`}
            deniedActionMsg={en.common.feedback.documentDenied}
            denyReasons={addressDenyOptions}
          />
        </Stack>
      </SimpleShowLayout>
    </Show>
  )
}
