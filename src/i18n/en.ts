import englishMessages from 'ra-language-english'

export const en = {
  ...englishMessages,
  common: {
    group: {
      userInfo: 'User Info',
      residencyInfo: 'Residency Info',
      kycInfo: 'KYC Info',
      referralInfo: 'Referral Info',
    },
    label: {
      firstName: 'First Name',
      lastName: 'Last Name',
      dateOfBirth: 'Date of Birth',
      age: 'Age - Months young',
      email: 'Email',
      verifiedEmail: 'Verified email',
      phoneNumber: 'Phone Number',
      verifiedNumber: 'Verified number',
      sex: 'Sex',
      investmentAccountStatus: 'Investment Account Status',
      residencyCountry: 'Residency Country',
      l0Status: 'L0 Status',
      l1Status: 'L1 Status',
      l2Status: 'L2 Status',
      referralCode: 'Referral Code',
      referredBy: 'Referred By',
      userId: 'User ID',
      name: 'Name',
      currentStatus: 'Current Status',
      isLoggedIn: 'Is Logged In',
      registerTime: 'Register Time',
      faceScanLastLivenessCheck: 'Last face login',
      permissions: 'Permissions',
      documentNumber: 'Document Number',
      documentType: 'Document Type',
      issuingCountry: 'Issuing Country',
      issueDate: 'Issue Date',
      expiryDate: 'Expiry Date',
      gender: 'Gender',
      idNumber: 'ID Number',
      templateName: 'Template Name',
      templateType: 'Template Type',
      barcodeValues: 'Barcode Values',
      nfcValues: 'NFC Values',
      userConfirmedValues: 'User Confirmed Values',
      scannedValues: 'Scanned Values',
      rawDocumentData: 'Raw Document Data',
      addressLine1: 'Address Line 1',
      addressLine2: 'Address Line 2',
      postalCode: 'Postal Code',
      city: 'City',
      state: 'State',
      country: 'Country',
      addressSubmissionDate: 'Submission Time',
      idReviewStatus: 'ID Review Status',
      idRejectionReason: 'ID Rejection Reason',
      verifiedId: 'Verified ID',
      verifiedName: 'Verified name',
    },
    placeholder: {
      searchByLastName: 'Search by last name',
      searchByFirstName: 'Search by first name',
    },
    error: {
      noRecord: 'Could not get record',
      genericError: 'Something went wrong',
      imageError: 'Error loading image, please try refreshing',
      unknownError: 'An unknown error occurred',
    },
    feedback: {
      confirmReview:
        'I have thoroughly reviewed this submission, and will be held accountable if incorrect.',
      approveSubmission: 'Are you sure you want to approve the user: %{email}?',
      documentApproved: 'Document approved',
      documentDenied: 'Document denied: %{email}',
      successfullyResentEnrollEmail: 'Successfully resent enroll email',
    },
    button: {
      closeAction: 'Close',
      approveDocument: 'Approve document',
      denyDocument: 'Deny document',
      officeNavigation: 'Enter',
    },
  },
  authentication: {
    label: {
      tontineEmail: 'Your tontine email',
    },
    button: {
      bioEnroll: 'Start face scan',
    },
    error: {
      genericFaceScanError: 'Face scan failed, please try again',
    },
    message: {
      noAutoFaceScan:
        'If face scan failed to start automatically, click the button below to start it manually.',
    },
  },
  operator: {
    label: {
      resendEmail: 'Resend email',
    },
    show: {
      userInfo: 'User Info',
    },
    list: {
      userId: 'User ID',
      name: 'Name',
      email: 'Email',
      currentStatus: 'Current Status',
      isLoggedIn: 'Is Logged In',
      faceScanLastLivenessCheck: 'Face Scan Last Liveness Check',
      registerTime: 'Register Time',
    },
    notification: {
      successfullyResentEnrollEmail: 'Successfully resent enroll email',
    },
  },
  kyc: {
    label: {
      from: 'From',
      to: 'To',
      status: 'Status',
      submitted: 'Submitted',
      analysis: 'Analysis',
      approved: 'Approved',
      denied: 'Denied',
      addressLine1: 'Address Line 1',
      addressLine2: 'Address Line 2',
      postalCode: 'Postal Code',
      city: 'City',
      state: 'State',
      country: 'Country',
      addressSubmissionDate: 'Submission Time',
      verifiedId: 'Verified ID',
      idReviewStatus: 'ID Review Status',
      idRejectionReason: 'ID Rejection Reason',
      documentType: 'Document Type',
      documentNumber: 'Document number',
      issuingCountry: 'Issuing Country',
      firstName: 'First Name',
      lastName: 'Last Name',
      dateOfBirth: 'Date of Birth',
      issueDate: 'Issue Date',
      expiryDate: 'Expiry Date',
      idSubmissionDate: 'Submission Time',
      denyTextReason: 'Deny reason',
      denyNameMismatch: 'Name mismatch',
      denyDocTooOld: 'Document too old',
      denyIllegibleDoc: 'Illegible document',
      denyDocumentNotShown: 'Document not shown',
      denyOtherReason: 'Other reason(comment below)',
      denyReasonRequired: 'Reason is required for this type',
      barcodeValues: 'Barcode Values',
      nfcValues: 'NFC Values',
      scannedValues: 'Scanned Values',
      templateInfo: 'Template Info',
      templateName: 'Template Name',
      templateType: 'Template Type',
      userConfirmedValues: 'User Confirmed Values',
    },
    group: {
      addressSubmission: 'Address Submission',
      addressDocument: 'Submitted document',
      idSubmission: 'ID Submission',
      idDocument: 'Submitted document',
      reviewDocument: 'Review document',
      userConfirmedValues: 'User confirmed values',
      scannedValues: 'Scanned values',
      rawDocumentData: 'Raw Document Data',
    },
    button: {
      seeInFacetecDashboard: 'See in facetec dashboard',
    },
  },
  dashboard: {
    title: 'Welcome to the Internal Dashboard',
    topBarTitle: 'Internal Dashboard',
    subtitle:
      'Tontine platform management dashboard. If faced with issues or questions',
    supportLink: 'click here for support.',
    feature: {
      tontineUsers: {
        title: 'Tontine Users',
        subtitle: 'Details about signed up users to the MyTontine app',
      },
      idDocumentReviewQueue: {
        title: 'Identity Verification Queue',
        subtitle:
          'Face scans and identity documents submitted by Tontine users to pass L1 KYC',
      },
      addressReviewQueue: {
        title: 'Address Verification Queue',
        subtitle:
          'Address verification requests submitted by tontine users to pass L2 KYC',
      },
      companyOperators: {
        title: 'Internal Dashboard operators',
        subtitle:
          'Browse and manage all Internal Dashboard dashboard operators',
      },
    },
  },
  mtUserManagement: {
    list: {
      idVerified: 'ID Verified',
    },
    show: {
      userInfo: 'User Info',
      residencyInfo: 'Residency Info',
      kycInfo: 'KYC Info',
      referralInfo: 'Referral Info',
    },
  },
} as const
